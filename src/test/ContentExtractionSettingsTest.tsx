// 内容提取设置测试组件
// 用于测试智能切换阈值的数据库存储和用户界面功能
import React, { useState, useEffect } from "react";
import { Card, Button, Space, Typography, Alert, Divider, message } from "antd";
import {
  SettingOutlined,
  ExperimentOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import {
  getLengthThreshold,
  setLengthThreshold,
  getLengthThresholdAsync,
} from "../config/contentExtractionConfig";
import { IndexedDBUISettingsStorage } from "../database/IndexedDBUISettingsStorage";
import { useUIStore } from "../stores/uiStore";

const { Title, Text, Paragraph } = Typography;

/**
 * 内容提取设置测试组件
 */
const ContentExtractionSettingsTest: React.FC = () => {
  const [currentThreshold, setCurrentThreshold] = useState<number>(1000);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const { openSettingsModal } = useUIStore();

  // 加载当前阈值
  useEffect(() => {
    const loadThreshold = async () => {
      try {
        const threshold = await getLengthThresholdAsync();
        setCurrentThreshold(threshold);
      } catch (error) {
        console.error("加载阈值失败:", error);
        setCurrentThreshold(getLengthThreshold());
      }
    };
    loadThreshold();
  }, []);

  // 添加测试结果
  const addTestResult = (result: string, success: boolean = true) => {
    const icon = success ? "✅" : "❌";
    setTestResults((prev) => [...prev, `${icon} ${result}`]);
  };

  // 清空测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  // 测试阈值设置和保存
  const testThresholdSetting = async () => {
    setLoading(true);
    clearResults();
    addTestResult("开始测试智能切换阈值设置功能...");

    try {
      // 测试 1: 获取当前阈值
      const originalThreshold = await getLengthThresholdAsync();
      addTestResult(`当前阈值: ${originalThreshold}字`);

      // 测试 2: 设置新阈值
      const newThreshold = 1500;
      addTestResult(`尝试设置新阈值: ${newThreshold}字`);
      await setLengthThreshold(newThreshold);

      // 测试 3: 验证内存中的值
      const memoryThreshold = getLengthThreshold();
      if (memoryThreshold === newThreshold) {
        addTestResult(`内存中阈值已更新: ${memoryThreshold}字`);
      } else {
        addTestResult(
          `内存中阈值更新失败，期望: ${newThreshold}, 实际: ${memoryThreshold}`,
          false
        );
      }

      // 测试 4: 验证数据库中的值
      const dbSettings =
        await IndexedDBUISettingsStorage.loadContentExtractionSettings();
      if (dbSettings && dbSettings.lengthThreshold === newThreshold) {
        addTestResult(`数据库中阈值已保存: ${dbSettings.lengthThreshold}字`);
      } else {
        addTestResult(
          `数据库中阈值保存失败，期望: ${newThreshold}, 实际: ${
            dbSettings?.lengthThreshold || "null"
          }`,
          false
        );
      }

      // 测试 5: 重新加载验证持久化
      const reloadedThreshold = await getLengthThresholdAsync();
      if (reloadedThreshold === newThreshold) {
        addTestResult(`重新加载后阈值正确: ${reloadedThreshold}字`);
      } else {
        addTestResult(
          `重新加载后阈值错误，期望: ${newThreshold}, 实际: ${reloadedThreshold}`,
          false
        );
      }

      // 测试 6: 恢复原始阈值
      addTestResult(`恢复原始阈值: ${originalThreshold}字`);
      await setLengthThreshold(originalThreshold);
      setCurrentThreshold(originalThreshold);

      addTestResult("智能切换阈值测试完成！");
      message.success("阈值设置测试完成");
    } catch (error) {
      addTestResult(`测试过程中出现错误: ${error}`, false);
      message.error("阈值设置测试失败");
    } finally {
      setLoading(false);
    }
  };

  // 测试数据库存储功能
  const testDatabaseStorage = async () => {
    setLoading(true);
    clearResults();
    addTestResult("开始测试数据库存储功能...");

    try {
      // 测试设置保存
      const testSettings = {
        lengthThreshold: 800,
        smartMode: {
          maxLength: 250,
          enableSmartTruncation: true,
        },
      };

      addTestResult("保存测试配置到数据库...");
      await IndexedDBUISettingsStorage.saveContentExtractionSettings(
        testSettings
      );
      addTestResult("配置保存成功");

      // 测试设置加载
      addTestResult("从数据库加载配置...");
      const loadedSettings =
        await IndexedDBUISettingsStorage.loadContentExtractionSettings();

      if (loadedSettings) {
        if (loadedSettings.lengthThreshold === testSettings.lengthThreshold) {
          addTestResult(`阈值加载正确: ${loadedSettings.lengthThreshold}字`);
        } else {
          addTestResult(
            `阈值加载错误，期望: ${testSettings.lengthThreshold}, 实际: ${loadedSettings.lengthThreshold}`,
            false
          );
        }

        if (
          loadedSettings.smartMode.maxLength ===
          testSettings.smartMode.maxLength
        ) {
          addTestResult(`智能模式配置加载正确`);
        } else {
          addTestResult(`智能模式配置加载错误`, false);
        }
      } else {
        addTestResult("配置加载失败", false);
      }

      // 恢复默认设置
      const defaultSettings = {
        lengthThreshold: 1000,
        smartMode: {
          maxLength: 300,
          enableSmartTruncation: true,
        },
      };
      await IndexedDBUISettingsStorage.saveContentExtractionSettings(
        defaultSettings
      );
      setCurrentThreshold(1000);
      addTestResult("已恢复默认配置");

      addTestResult("数据库存储测试完成！");
      message.success("数据库存储测试完成");
    } catch (error) {
      addTestResult(`数据库测试过程中出现错误: ${error}`, false);
      message.error("数据库存储测试失败");
    } finally {
      setLoading(false);
    }
  };

  // 打开设置页面
  const openSettings = () => {
    openSettingsModal("notes");
    message.info("已打开便签设置页面，请测试智能切换阈值设置");
  };

  return (
    <div style={{ padding: 20, maxWidth: 800, margin: "0 auto" }}>
      <Title level={2}>
        <ExperimentOutlined style={{ marginRight: 8 }} />
        内容提取设置测试
      </Title>

      <Paragraph>
        此测试页面用于验证智能切换阈值的数据库存储功能和用户界面交互。
      </Paragraph>

      {/* 当前状态 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Title level={4}>
          <CheckCircleOutlined style={{ marginRight: 8 }} />
          当前状态
        </Title>
        <Space direction="vertical" style={{ width: "100%" }}>
          <div>
            <Text strong>当前智能切换阈值：</Text>
            <Text code>{currentThreshold} 字</Text>
          </div>
          <div>
            <Text type="secondary">
              超过此字数的连接便签将自动使用智能模式进行内容提取
            </Text>
          </div>
        </Space>
      </Card>

      {/* 测试操作 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Title level={4}>
          <SettingOutlined style={{ marginRight: 8 }} />
          测试操作
        </Title>
        <Space wrap>
          <Button
            type="primary"
            onClick={testThresholdSetting}
            loading={loading}
          >
            测试阈值设置
          </Button>
          <Button onClick={testDatabaseStorage} loading={loading}>
            测试数据库存储
          </Button>
          <Button onClick={openSettings}>打开设置页面</Button>
          <Button onClick={clearResults}>清空结果</Button>
        </Space>
      </Card>

      {/* 测试结果 */}
      {testResults.length > 0 && (
        <Card size="small">
          <Title level={4}>
            <ExclamationCircleOutlined style={{ marginRight: 8 }} />
            测试结果
          </Title>
          <div
            style={{
              background: "#f5f5f5",
              padding: 12,
              borderRadius: 4,
              fontFamily: "monospace",
              maxHeight: 400,
              overflowY: "auto",
            }}
          >
            {testResults.map((result, index) => (
              <div key={index} style={{ marginBottom: 4 }}>
                {result}
              </div>
            ))}
          </div>
        </Card>
      )}

      <Divider />

      {/* 使用说明 */}
      <Alert
        message="测试说明"
        description={
          <div>
            <p>
              <strong>阈值设置测试：</strong>
              验证阈值修改、内存更新、数据库保存和持久化加载功能。
            </p>
            <p>
              <strong>数据库存储测试：</strong>
              验证完整的内容提取配置的保存和加载功能。
            </p>
            <p>
              <strong>UI测试：</strong>
              点击"打开设置页面"在实际界面中测试阈值设置功能。
            </p>
          </div>
        }
        type="info"
        showIcon
      />
    </div>
  );
};

export default ContentExtractionSettingsTest;
