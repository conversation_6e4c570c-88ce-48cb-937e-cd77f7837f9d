<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown 渲染测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .test-section {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }

        .markdown-input {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin-bottom: 20px;
        }

        .html-output {
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 4px;
            background: white;
        }

        h1,
        h2,
        h3 {
            color: #333;
        }

        .task-list {
            list-style: none;
            padding-left: 0;
        }

        .task-item {
            margin: 5px 0;
        }

        .task-item input {
            margin-right: 8px;
        }

        blockquote {
            border-left: 4px solid #ddd;
            margin: 0;
            padding-left: 16px;
            color: #666;
        }

        pre {
            background: #f8f8f8;
            padding: 12px;
            border-radius: 4px;
            overflow-x: auto;
        }

        code {
            background: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        .tiptap-table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
            border: 1px solid #ddd;
        }

        .tiptap-table th,
        .tiptap-table td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }

        .tiptap-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .tiptap-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        del {
            color: #999;
            text-decoration: line-through;
        }

        a {
            color: #0066cc;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        hr {
            border: none;
            border-top: 2px solid #ddd;
            margin: 20px 0;
        }
    </style>
</head>

<body>
    <h1>🧪 Markdown 渲染测试</h1>
    <p>这个页面用于测试我们的 Markdown 转 HTML 功能是否正常工作。</p>

    <div class="test-section">
        <h2>测试 1: 基础格式</h2>
        <div class="markdown-input"># 主标题

            ## 二级标题

            这是一个段落，包含 **粗体文本** 和 *斜体文本*。

            还有 `行内代码` 和普通文本。</div>
        <div class="html-output" id="test1-output"></div>
    </div>

    <div class="test-section">
        <h2>测试 2: 列表和任务</h2>
        <div class="markdown-input">### 无序列表
            - 第一项
            - 第二项
            - 第三项

            ### 食材清单
            - 五花肉50克（肥瘦相间）
            - 葱2根、姜3片、蒜5瓣
            - 八角2颗、桂皮1小段、香叶2片
            - 生抽3勺、老抽1勺、料酒2勺
            - 冰糖/白砂糖50克、热水适量

            ### 任务列表
            - [ ] 未完成任务
            - [x] 已完成任务
            - [ ] 另一个未完成任务

            ### 混合列表
            - 普通列表项
            - [ ] 任务项1
            - 另一个普通项
            - [x] 已完成任务
            - 最后一个普通项</div>
        <div class="html-output" id="test2-output"></div>
    </div>

    <div class="test-section">
        <h2>测试 3: 代码和引用</h2>
        <div class="markdown-input">```javascript
            function hello() {
            console.log("Hello, World!");
            }
            ```

            > 这是一个引用块
            > 可以包含多行内容</div>
        <div class="html-output" id="test3-output"></div>
    </div>

    <div class="test-section">
        <h2>测试 4: 表格支持</h2>
        <div class="markdown-input">### 数据对比表格

            | 功能 | 状态 | 优先级 | 备注 |
            |------|:----:|-------:|------|
            | 基础编辑 | ✅ 完成 | 高 | 核心功能 |
            | 表格支持 | 🔄 进行中 | 中 | 增强功能 |
            | AI生成 | ✅ 完成 | 高 | 重要特性 |

            ### 简单表格

            | 左对齐 | 居中 | 右对齐 |
            |--------|:----:|-------:|
            | 内容1 | 内容2 | 内容3 |
            | 长一点的内容 | 短 | 123 |</div>
        <div class="html-output" id="test4-output"></div>
    </div>

    <div class="test-section">
        <h2>测试 5: 高级格式</h2>
        <div class="markdown-input">### 链接和删除线

            这里有一个 [链接到百度](https://www.baidu.com) 的示例。

            ~~这是删除线文本~~ 和正常文本。

            ---

            ### 混合格式

            **粗体中包含 *斜体* 文本**

            `代码中包含 **粗体** 不会转换`

            > 引用中包含 **粗体** 和 *斜体*
            >
            > 还有 `行内代码`</div>
        <div class="html-output" id="test5-output"></div>
    </div>

    <div class="test-section">
        <h2>测试 6: AI 生成典型内容</h2>
        <div class="markdown-input"># 📊 数据分析报告

            根据您的问题，我来为您分析：

            ## 🎯 主要观点

            1. **重要性**: 这个问题很重要
            2. **复杂性**: 需要多角度考虑

            ### 📋 具体建议

            - [ ] 第一步：收集信息
            - [ ] 第二步：分析数据
            - [x] 第三步：制定方案

            ### 📈 数据对比

            | 指标 | 当前值 | 目标值 | 状态 |
            |------|:------:|:------:|:----:|
            | 性能 | 85% | 95% | 🔄 |
            | 质量 | 92% | 90% | ✅ |
            | 用户满意度 | 88% | 90% | 🔄 |

            ```python
            def solve_problem():
            """解决方案实现"""
            return "优化后的解决方案"
            ```

            > 💡 **提示**: 记住要持续优化
            >
            > 详细信息请参考 [官方文档](https://example.com)

            ---

            **总结**: ~~旧方案~~ 新方案更加有效。</div>
        <div class="html-output" id="test6-output"></div>
    </div>

    <div class="test-section">
        <h2>测试 7: 边界情况</h2>
        <div class="markdown-input">### 空表格测试

            | 列1 | 列2 |
            |-----|-----|

            ### 不完整表格

            | 只有表头 |
            |----------|

            ### 混合内容

            普通段落

            | 表格 | 内容 |
            |------|------|
            | 行1 | 数据1 |

            继续普通段落

            - 列表项1
            - 列表项2

            最后一个段落</div>
        <div class="html-output" id="test7-output"></div>
    </div>

    <script>
        // 复制 BasicEditor 中的转换函数
        function convertMarkdownToHTML(markdown) {
            let html = markdown;

            // 🎯 首先处理表格（必须在其他转换之前）
            html = convertMarkdownTables(html);

            // 转换标题
            html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
            html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
            html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

            // 转换粗体和斜体
            html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

            // 转换删除线
            html = html.replace(/~~(.*?)~~/g, '<del>$1</del>');

            // 转换行内代码
            html = html.replace(/`(.*?)`/g, '<code>$1</code>');

            // 转换代码块
            html = html.replace(/```(\w*)\n([\s\S]*?)\n```/g, '<pre><code class="language-$1">$2</code></pre>');

            // 转换链接
            html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

            // 🎯 修复列表转换逻辑：先处理任务列表，再处理普通列表
            // 转换任务列表（必须在普通列表之前）
            html = html.replace(/^- \[x\] (.*$)/gim, '<ul class="task-list"><li class="task-item"><input type="checkbox" checked disabled> $1</li></ul>');
            html = html.replace(/^- \[ \] (.*$)/gim, '<ul class="task-list"><li class="task-item"><input type="checkbox" disabled> $1</li></ul>');

            // 转换无序列表（排除已经转换的任务列表）
            html = html.replace(/^- (?!\[[ x]\])(.*$)/gim, '<ul><li>$1</li></ul>');

            // 转换有序列表
            html = html.replace(/^\d+\. (.*$)/gim, '<ol><li>$1</li></ol>');

            // 合并连续的列表项
            html = html.replace(/<\/ul>\s*<ul>/g, '');
            html = html.replace(/<\/ol>\s*<ol>/g, '');
            html = html.replace(/<\/ul>\s*<ul class="task-list">/g, '');
            html = html.replace(/<\/ul class="task-list">\s*<ul>/g, '');

            // 转换引用
            html = html.replace(/^> (.*$)/gim, '<blockquote><p>$1</p></blockquote>');

            // 转换水平线
            html = html.replace(/^---$/gim, '<hr>');

            // 转换段落
            const lines = html.split('\n');
            const processedLines = lines.map(line => {
                const trimmed = line.trim();
                if (!trimmed) return '';

                // 如果已经是HTML标签，不需要包装
                if (trimmed.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|hr|table|div)/)) {
                    return trimmed;
                }

                // 包装为段落
                return `<p>${trimmed}</p>`;
            });

            return processedLines.filter(line => line).join('\n');
        }

        // 转换Markdown表格为HTML
        function convertMarkdownTables(text) {
            const lines = text.split('\n');
            const result = [];
            let i = 0;

            while (i < lines.length) {
                const line = lines[i].trim();

                // 检测表格开始（包含 | 的行）
                if (line.includes('|') && line.split('|').length >= 3) {
                    const tableLines = [];
                    let j = i;

                    // 收集连续的表格行
                    while (j < lines.length && lines[j].trim().includes('|')) {
                        tableLines.push(lines[j].trim());
                        j++;
                    }

                    // 如果至少有2行（表头+分隔符），处理为表格
                    if (tableLines.length >= 2) {
                        const tableHTML = convertTableLines(tableLines);
                        if (tableHTML) {
                            result.push(tableHTML);
                            i = j;
                            continue;
                        }
                    }
                }

                result.push(lines[i]);
                i++;
            }

            return result.join('\n');
        }

        // 转换表格行为HTML表格
        function convertTableLines(tableLines) {
            if (tableLines.length < 2) return null;

            // 解析表头
            const headerCells = tableLines[0]
                .split('|')
                .map(cell => cell.trim())
                .filter(cell => cell);

            // 检查分隔符行
            const separatorLine = tableLines[1];
            if (!separatorLine.includes('-')) return null;

            // 解析对齐方式
            const alignments = separatorLine
                .split('|')
                .map(cell => cell.trim())
                .filter(cell => cell)
                .map(cell => {
                    if (cell.startsWith(':') && cell.endsWith(':')) return 'center';
                    if (cell.endsWith(':')) return 'right';
                    return 'left';
                });

            // 构建表格HTML
            let tableHTML = '<table class="tiptap-table">';

            // 表头
            tableHTML += '<thead><tr>';
            headerCells.forEach((cell, index) => {
                const align = alignments[index] || 'left';
                tableHTML += `<th style="text-align: ${align}">${cell}</th>`;
            });
            tableHTML += '</tr></thead>';

            // 表体
            if (tableLines.length > 2) {
                tableHTML += '<tbody>';
                for (let i = 2; i < tableLines.length; i++) {
                    const rowCells = tableLines[i]
                        .split('|')
                        .map(cell => cell.trim())
                        .filter(cell => cell);

                    if (rowCells.length > 0) {
                        tableHTML += '<tr>';
                        rowCells.forEach((cell, index) => {
                            const align = alignments[index] || 'left';
                            tableHTML += `<td style="text-align: ${align}">${cell}</td>`;
                        });
                        tableHTML += '</tr>';
                    }
                }
                tableHTML += '</tbody>';
            }

            tableHTML += '</table>';
            return tableHTML;
        }

        // 测试所有示例
        function runTests() {
            const tests = [
                { id: 'test1-output', content: document.querySelectorAll('.markdown-input')[0].textContent },
                { id: 'test2-output', content: document.querySelectorAll('.markdown-input')[1].textContent },
                { id: 'test3-output', content: document.querySelectorAll('.markdown-input')[2].textContent },
                { id: 'test4-output', content: document.querySelectorAll('.markdown-input')[3].textContent },
                { id: 'test5-output', content: document.querySelectorAll('.markdown-input')[4].textContent },
                { id: 'test6-output', content: document.querySelectorAll('.markdown-input')[5].textContent },
                { id: 'test7-output', content: document.querySelectorAll('.markdown-input')[6].textContent }
            ];

            tests.forEach((test, index) => {
                const output = document.getElementById(test.id);
                const html = convertMarkdownToHTML(test.content);
                output.innerHTML = html;

                // 添加调试信息
                console.log(`测试 ${index + 1}:`, {
                    originalLength: test.content.length,
                    htmlLength: html.length,
                    preview: test.content.substring(0, 100) + '...'
                });
            });
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>

</html>