// 内容提取设置组件 - 极简化版
import React, { useState, useEffect } from "react";
import {
  Card,
  Typography,
  InputNumber,
  Space,
  message,
  Button,
  Modal,
} from "antd";
import {
  SettingOutlined,
  ExclamationCircleOutlined,
  ExclamationCircleFilled,
} from "@ant-design/icons";
import {
  ContentExtractionConfigManager,
  setLengthThreshold,
  getLengthThresholdAsync,
} from "../../config/contentExtractionConfig";
import CardSectionTitle from "../common/CardSectionTitle";

const { Text } = Typography;

interface ContentExtractionSettingsProps {
  // 简化后不再需要配置变更回调
}

/**
 * 内容提取设置组件 - 延迟保存版
 * 🎯 显示智能切换阈值设置，支持变更提醒和手动保存
 */
export const ContentExtractionSettings: React.FC<
  ContentExtractionSettingsProps
> = () => {
  const [threshold, setThreshold] = useState<number>(1000);
  const [originalThreshold, setOriginalThreshold] = useState<number>(1000);
  const [loading, setLoading] = useState<boolean>(false);
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const configManager = ContentExtractionConfigManager.getInstance();

  // 初始化阈值 - 从数据库加载
  useEffect(() => {
    const loadThreshold = async () => {
      try {
        const savedThreshold = await getLengthThresholdAsync();
        setThreshold(savedThreshold);
        setOriginalThreshold(savedThreshold);
      } catch (error) {
        console.error("加载智能切换阈值失败:", error);
        const fallbackThreshold = configManager.getLengthThreshold();
        setThreshold(fallbackThreshold);
        setOriginalThreshold(fallbackThreshold);
      }
    };
    loadThreshold();
  }, [configManager]);

  // 处理阈值变更
  const handleThresholdChange = (value: number | null) => {
    if (value && value > 0) {
      setThreshold(value);
      setHasChanges(value !== originalThreshold);
    }
  };

  // 保存设置
  const handleSave = async () => {
    setLoading(true);
    try {
      await setLengthThreshold(threshold);
      setOriginalThreshold(threshold);
      setHasChanges(false);
      message.success("智能切换阈值已保存", 1.5);
    } catch (error) {
      console.error("保存智能切换阈值失败:", error);
      message.error("保存失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  // 重置设置
  const handleReset = () => {
    Modal.confirm({
      title: "确认重置",
      icon: <ExclamationCircleFilled />,
      content: "确定要将智能切换阈值重置为默认值（1000字）吗？",
      okText: "确定重置",
      cancelText: "取消",
      onOk: async () => {
        setLoading(true);
        try {
          const defaultThreshold = 1000;
          await setLengthThreshold(defaultThreshold);
          setThreshold(defaultThreshold);
          setOriginalThreshold(defaultThreshold);
          setHasChanges(false);
          message.success("已重置为默认值");
        } catch (error) {
          console.error("重置失败:", error);
          message.error("重置失败，请重试");
        } finally {
          setLoading(false);
        }
      },
    });
  };

  return (
    <Card size="small" style={{ marginBottom: 16 }}>
      <CardSectionTitle icon={<SettingOutlined />}>
        内容提取优化
      </CardSectionTitle>

      <Space direction="vertical" style={{ width: "100%" }}>
        {/* 说明文字 */}
        <Text type="secondary" style={{ fontSize: "14px" }}>
          💡 系统会根据连接便签的总字数自动选择最佳模式
        </Text>

        {/* 阈值设置 */}
        <div>
          <Text strong style={{ marginBottom: 8, display: "block" }}>
            智能切换阈值
          </Text>
          <Space align="center">
            <Text>超过</Text>
            <InputNumber
              value={threshold}
              onChange={handleThresholdChange}
              min={100}
              max={5000}
              step={100}
              style={{ width: 100 }}
              disabled={loading}
            />
            <Text>字时自动切换到智能模式</Text>
          </Space>
          <Text
            type="secondary"
            style={{ fontSize: "12px", marginTop: "8px", display: "block" }}
          >
            精准模式：使用完整内容 | 智能模式：提取核心内容
          </Text>
        </div>

        {/* 设置变更状态提示 */}
        {hasChanges && (
          <div
            style={{
              marginTop: 12,
              padding: 8,
              backgroundColor: "#fff7e6",
              border: "1px solid #ffd591",
              borderRadius: 4,
            }}
          >
            <Text style={{ fontSize: "12px", color: "#d46b08" }}>
              <ExclamationCircleOutlined style={{ marginRight: 4 }} />
              设置已修改，请点击"保存设置"按钮保存更改
            </Text>
          </div>
        )}

        {/* 操作按钮 */}
        <div
          style={{
            marginTop: 16,
            display: "flex",
            gap: 8,
            justifyContent: "flex-end",
          }}
        >
          <Button onClick={handleReset} size="small" disabled={loading}>
            重置默认值
          </Button>
          <Button
            type="primary"
            onClick={handleSave}
            disabled={!hasChanges || loading}
            size="small"
            loading={loading}
          >
            保存设置
          </Button>
        </div>
      </Space>
    </Card>
  );
};

export default ContentExtractionSettings;
