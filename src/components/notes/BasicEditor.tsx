/**
 * 基础 Tiptap 编辑器组件
 * 严格按照官方文档实现，只包含最基本的功能
 * 参考：https://tiptap.dev/docs/editor/getting-started/install/react
 */

import React, { useCallback, useEffect, useRef, useState } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import { Table } from "@tiptap/extension-table";
import { TableRow } from "@tiptap/extension-table-row";
import { TableCell } from "@tiptap/extension-table-cell";
import { TableHeader } from "@tiptap/extension-table-header";
import { TaskList } from "@tiptap/extension-task-list";
import { TaskItem } from "@tiptap/extension-task-item";
import BasicToolbar from "./BasicToolbar";
import "./BasicToolbar.css";

/**
 * 基础编辑器属性接口
 */
interface BasicEditorProps {
  /** 编辑器内容 */
  content?: string;
  /** 内容变化回调 */
  onChange?: (content: string) => void;
  /** 编辑器准备就绪回调 */
  onEditorReady?: (editor: any) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autoFocus?: boolean;
  /** 是否可编辑 */
  editable?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 点击事件 */
  onClick?: (e: React.MouseEvent) => void;
  /** 鼠标按下事件 */
  onMouseDown?: (e: React.MouseEvent) => void;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 标题属性 */
  title?: string;
  /** 是否显示工具栏 */
  showToolbar?: boolean;
  /** 是否启用智能滚动（AI生成内容时自动滚动到底部） */
  enableAutoScroll?: boolean;
}

/**
 * 基础编辑器组件
 * 使用 Tiptap 官方推荐的最简单配置
 */
const BasicEditor: React.FC<BasicEditorProps> = ({
  content = "",
  onChange,
  onEditorReady,
  placeholder = "开始输入...",
  autoFocus = false,
  editable = true,
  className = "",
  onClick,
  onMouseDown,
  style,
  title,
  showToolbar = false,
  enableAutoScroll = false,
}) => {
  // 用于检测滚动条的 ref
  const editorContentRef = useRef<HTMLDivElement>(null);

  // 用于跟踪上一次内容长度，实现智能滚动
  const prevContentLengthRef = useRef<number>(0);

  // 用于防抖滚动操作
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 用于显示滚动指示器
  const [isAutoScrolling, setIsAutoScrolling] = useState(false);

  // 用于检测用户是否手动滚动
  const userScrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  /**
   * 检测用户手动滚动
   * 当用户手动滚动时，暂停自动滚动一段时间
   */
  const handleUserScroll = useCallback(() => {
    // 标记用户正在滚动
    setIsUserScrolling(true);

    // 清除之前的定时器
    if (userScrollTimeoutRef.current) {
      clearTimeout(userScrollTimeoutRef.current);
    }

    // 2秒后恢复自动滚动
    userScrollTimeoutRef.current = setTimeout(() => {
      setIsUserScrolling(false);
    }, 2000);
  }, []);

  /**
   * 智能滚动到底部
   * 在AI生成内容时自动跟随最新内容，提供平滑的用户体验
   */
  const scrollToBottom = useCallback(
    (smooth: boolean = true) => {
      if (!editorContentRef.current) return;

      // 如果用户正在手动滚动，不执行自动滚动
      if (isUserScrolling) return;

      const editorContent = editorContentRef.current;

      // 清除之前的滚动定时器
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      // 显示滚动指示器
      setIsAutoScrolling(true);

      // 使用requestAnimationFrame确保DOM已更新
      scrollTimeoutRef.current = setTimeout(() => {
        try {
          // 滚动到编辑器内容的底部
          editorContent.scrollTo({
            top: editorContent.scrollHeight,
            behavior: smooth ? "smooth" : "auto",
          });

          // 滚动完成后隐藏指示器
          setTimeout(() => setIsAutoScrolling(false), smooth ? 500 : 100);
        } catch (error) {
          // 降级处理：直接设置scrollTop
          editorContent.scrollTop = editorContent.scrollHeight;
          setIsAutoScrolling(false);
        }
      }, 0);
    },
    [isUserScrolling]
  );

  /**
   * 检测编辑器是否有滚动条
   * 通过比较 scrollHeight 和 clientHeight 来判断
   */
  const checkScrollbar = useCallback(() => {
    if (!editorContentRef.current) return;

    const editorContent = editorContentRef.current;
    const proseMirror = editorContent.querySelector(
      ".ProseMirror"
    ) as HTMLDivElement;

    if (!proseMirror) return;

    // 检测是否有垂直滚动条
    const hasVerticalScrollbar =
      editorContent.scrollHeight > editorContent.clientHeight;

    // 设置 data-scrollable 属性用于 CSS 选择器
    proseMirror.setAttribute(
      "data-scrollable",
      hasVerticalScrollbar.toString()
    );

    // 为不支持 :has() 选择器的浏览器添加类名
    const stickyNoteContent = editorContent.closest(".sticky-note-content");
    if (stickyNoteContent) {
      if (hasVerticalScrollbar) {
        stickyNoteContent.classList.add("has-scrollbar");
      } else {
        stickyNoteContent.classList.remove("has-scrollbar");
      }
    }
  }, []);
  // 使用官方推荐的 useEditor hook
  const editor = useEditor({
    // 使用 StarterKit 扩展，包含最常用的基础功能
    extensions: [
      StarterKit.configure({
        // 可以在这里配置 StarterKit 的选项
        // 暂时使用默认配置
      }),
      // 添加 Placeholder 扩展，按照官方文档配置
      Placeholder.configure({
        placeholder: placeholder,
        emptyEditorClass: "is-editor-empty",
        emptyNodeClass: "is-empty",
        showOnlyWhenEditable: true,
        showOnlyCurrent: true,
      }),
      // 添加表格扩展，按照官方文档配置
      Table.configure({
        resizable: true, // 启用表格列宽调整
        HTMLAttributes: {
          class: "tiptap-table",
        },
      }),
      TableRow,
      TableHeader,
      TableCell,
      // 添加任务列表扩展，按照官方文档配置
      TaskList.configure({
        HTMLAttributes: {
          class: "task-list",
        },
      }),
      // 添加任务项扩展，任务列表需要此扩展
      TaskItem.configure({
        HTMLAttributes: {
          class: "task-item",
        },
      }),
    ],
    // 设置初始内容
    content: content,
    // 设置是否可编辑
    editable: editable,
    // 设置是否自动聚焦
    autofocus: autoFocus,
    // 内容更新回调
    onUpdate: ({ editor }) => {
      if (onChange) {
        // 获取 HTML 格式的内容
        const html = editor.getHTML();
        onChange(html);
      }
      // 内容更新后检测滚动条
      setTimeout(checkScrollbar, 0);

      // 🎯 智能滚动：如果内容长度增加且启用了自动滚动，自动滚动到底部
      const currentContentLength = editor.getHTML().length;
      if (
        enableAutoScroll &&
        currentContentLength > prevContentLengthRef.current
      ) {
        // 内容增加了，触发智能滚动
        setTimeout(() => scrollToBottom(true), 50); // 延迟50ms确保DOM更新完成
      }
      prevContentLengthRef.current = currentContentLength;
    },
    // 编辑器创建完成回调
    onCreate: ({ editor }) => {
      if (onEditorReady) {
        onEditorReady(editor);
      }
      // 编辑器创建后检测滚动条
      setTimeout(checkScrollbar, 0);

      // 初始化内容长度
      prevContentLengthRef.current = editor.getHTML().length;
    },
  });

  /**
   * 智能内容处理函数
   * 自动检测内容格式并进行相应转换
   * @param rawContent 原始内容
   * @returns 处理后的HTML内容
   */
  const processContent = useCallback((rawContent: string) => {
    if (!rawContent) return "";

    const trimmed = rawContent.trim();

    // 检测是否为HTML内容（已经是富文本格式）
    if (trimmed.startsWith("<") && trimmed.includes(">")) {
      return rawContent; // 直接返回HTML，无需转换
    }

    // 检测是否为Markdown内容（AI生成或手动输入的Markdown）
    const markdownPatterns = [
      /^#{1,6}\s+/m, // 标题：# ## ###
      /\*\*.*?\*\*/g, // 粗体：**text**
      /\*.*?\*/g, // 斜体：*text*
      /~~.*?~~/g, // 删除线：~~text~~
      /^```/m, // 代码块：```code```
      /^- \[[ x]\]/m, // 任务列表：- [x] - [ ]
      /^[-*+]\s+/m, // 无序列表：- item
      /^\d+\.\s+/m, // 有序列表：1. item
      /^>\s+/m, // 引用：> quote
      /\|.*\|/m, // 表格：| col1 | col2 |
      /\[.*?\]\(.*?\)/g, // 链接：[text](url)
      /^---$/m, // 水平线：---
    ];

    const isMarkdown = markdownPatterns.some((pattern) =>
      pattern.test(trimmed)
    );

    if (isMarkdown) {
      // Markdown内容转换为HTML，确保富文本显示
      return convertMarkdownToHTML(rawContent);
    }

    // 纯文本，包装为段落并处理换行
    return `<p>${rawContent.replace(/\n/g, "<br>")}</p>`;
  }, []);

  /**
   * Markdown到HTML转换函数
   * 支持所有常用的Markdown语法，确保与Tiptap编辑器兼容
   * @param markdown Markdown格式的文本
   * @returns HTML格式的文本
   */
  const convertMarkdownToHTML = useCallback((markdown: string) => {
    let html = markdown;

    // 🎯 第一步：处理表格（必须在其他转换之前，避免干扰）
    html = convertMarkdownTables(html);

    // 🎯 第二步：转换标题（从大到小，避免嵌套匹配）
    html = html.replace(/^### (.*$)/gim, "<h3>$1</h3>");
    html = html.replace(/^## (.*$)/gim, "<h2>$1</h2>");
    html = html.replace(/^# (.*$)/gim, "<h1>$1</h1>");

    // 🎯 第三步：转换行内格式
    html = html.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>"); // 粗体
    html = html.replace(/\*(.*?)\*/g, "<em>$1</em>"); // 斜体
    html = html.replace(/~~(.*?)~~/g, "<del>$1</del>"); // 删除线
    html = html.replace(/`(.*?)`/g, "<code>$1</code>"); // 行内代码

    // 🎯 第四步：转换代码块
    html = html.replace(
      /```(\w*)\n([\s\S]*?)\n```/g,
      '<pre><code class="language-$1">$2</code></pre>'
    );

    // 🎯 第五步：转换链接
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

    // 🎯 修复列表转换逻辑：先处理任务列表，再处理普通列表
    // 转换任务列表（必须在普通列表之前）
    html = html.replace(
      /^- \[x\] (.*$)/gim,
      '<ul class="task-list"><li class="task-item"><input type="checkbox" checked disabled> $1</li></ul>'
    );
    html = html.replace(
      /^- \[ \] (.*$)/gim,
      '<ul class="task-list"><li class="task-item"><input type="checkbox" disabled> $1</li></ul>'
    );

    // 转换无序列表（排除已经转换的任务列表）
    html = html.replace(/^- (?!\[[ x]\])(.*$)/gim, "<ul><li>$1</li></ul>");

    // 转换有序列表
    html = html.replace(/^\d+\. (.*$)/gim, "<ol><li>$1</li></ol>");

    // 合并连续的列表项
    html = html.replace(/<\/ul>\s*<ul>/g, "");
    html = html.replace(/<\/ol>\s*<ol>/g, "");
    html = html.replace(/<\/ul>\s*<ul class="task-list">/g, "");
    html = html.replace(/<\/ul class="task-list">\s*<ul>/g, "");

    // 转换引用
    html = html.replace(/^> (.*$)/gim, "<blockquote><p>$1</p></blockquote>");

    // 转换水平线
    html = html.replace(/^---$/gim, "<hr>");

    // 转换段落
    const lines = html.split("\n");
    const processedLines = lines.map((line) => {
      const trimmed = line.trim();
      if (!trimmed) return "";

      // 如果已经是HTML标签，不需要包装
      if (trimmed.match(/^<(h[1-6]|ul|ol|li|blockquote|pre|hr|table|div)/)) {
        return trimmed;
      }

      // 包装为段落
      return `<p>${trimmed}</p>`;
    });

    return processedLines.filter((line) => line).join("\n");
  }, []);

  // 转换Markdown表格为HTML
  const convertMarkdownTables = useCallback((text: string) => {
    const lines = text.split("\n");
    const result = [];
    let i = 0;

    while (i < lines.length) {
      const line = lines[i].trim();

      // 检测表格开始（包含 | 的行）
      if (line.includes("|") && line.split("|").length >= 3) {
        const tableLines = [];
        let j = i;

        // 收集连续的表格行
        while (j < lines.length && lines[j].trim().includes("|")) {
          tableLines.push(lines[j].trim());
          j++;
        }

        // 如果至少有2行（表头+分隔符），处理为表格
        if (tableLines.length >= 2) {
          const tableHTML = convertTableLines(tableLines);
          if (tableHTML) {
            result.push(tableHTML);
            i = j;
            continue;
          }
        }
      }

      result.push(lines[i]);
      i++;
    }

    return result.join("\n");
  }, []);

  // 转换表格行为HTML表格
  const convertTableLines = useCallback((tableLines: string[]) => {
    if (tableLines.length < 2) return null;

    // 解析表头
    const headerCells = tableLines[0]
      .split("|")
      .map((cell) => cell.trim())
      .filter((cell) => cell);

    // 检查分隔符行
    const separatorLine = tableLines[1];
    if (!separatorLine.includes("-")) return null;

    // 解析对齐方式
    const alignments = separatorLine
      .split("|")
      .map((cell) => cell.trim())
      .filter((cell) => cell)
      .map((cell) => {
        if (cell.startsWith(":") && cell.endsWith(":")) return "center";
        if (cell.endsWith(":")) return "right";
        return "left";
      });

    // 构建表格HTML
    let tableHTML = '<table class="tiptap-table">';

    // 表头
    tableHTML += "<thead><tr>";
    headerCells.forEach((cell, index) => {
      const align = alignments[index] || "left";
      tableHTML += `<th style="text-align: ${align}">${cell}</th>`;
    });
    tableHTML += "</tr></thead>";

    // 表体
    if (tableLines.length > 2) {
      tableHTML += "<tbody>";
      for (let i = 2; i < tableLines.length; i++) {
        const rowCells = tableLines[i]
          .split("|")
          .map((cell) => cell.trim())
          .filter((cell) => cell);

        if (rowCells.length > 0) {
          tableHTML += "<tr>";
          rowCells.forEach((cell, index) => {
            const align = alignments[index] || "left";
            tableHTML += `<td style="text-align: ${align}">${cell}</td>`;
          });
          tableHTML += "</tr>";
        }
      }
      tableHTML += "</tbody>";
    }

    tableHTML += "</table>";
    return tableHTML;
  }, []);

  // 当外部 content 发生变化时，更新编辑器内容
  useEffect(() => {
    if (!editor) return;

    // 获取当前编辑器的HTML内容
    const currentHTML = editor.getHTML();

    // 如果外部内容与当前编辑器内容相同，跳过更新
    if (content === currentHTML) return;

    // 🎯 关键修复：智能处理内容格式
    const processedContent = processContent(content);

    console.log("📝 更新编辑器内容:", {
      originalLength: content.length,
      processedLength: processedContent.length,
      contentPreview: content.substring(0, 50) + "...",
      isHTML: content.trim().startsWith("<"),
    });

    // 检查内容是否增加（用于判断是否需要自动滚动）
    const prevLength = prevContentLengthRef.current;
    const newLength = processedContent.length;
    const isContentIncreasing = newLength > prevLength;

    // 使用处理后的内容更新编辑器
    editor.commands.setContent(processedContent, { emitUpdate: false });

    // 🎯 智能滚动：如果内容增加且编辑器不可编辑（流式生成状态）且启用了自动滚动，自动滚动到底部
    if (enableAutoScroll && isContentIncreasing && !editable) {
      setTimeout(() => scrollToBottom(true), 100); // 延迟确保内容渲染完成
    }

    // 更新内容长度记录
    prevContentLengthRef.current = newLength;
  }, [
    content,
    editor,
    processContent,
    editable,
    scrollToBottom,
    enableAutoScroll,
  ]);

  // 当 editable 属性变化时，更新编辑器的可编辑状态
  useEffect(() => {
    if (editor) {
      editor.setEditable(editable);
    }
  }, [editable, editor]);

  // 监听窗口大小变化和便签大小变化，重新检测滚动条
  useEffect(() => {
    const handleResize = () => {
      // 延迟检测，确保DOM已更新
      setTimeout(checkScrollbar, 100);
    };

    // 监听窗口大小变化
    window.addEventListener("resize", handleResize);

    // 使用 ResizeObserver 监听编辑器容器大小变化（便签大小调整）
    let resizeObserver: ResizeObserver | null = null;
    if (editorContentRef.current) {
      resizeObserver = new ResizeObserver(handleResize);
      resizeObserver.observe(editorContentRef.current);
    }

    return () => {
      window.removeEventListener("resize", handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [checkScrollbar]);

  // 监听画布缩放变化，重新检测滚动条
  useEffect(() => {
    // 画布缩放会影响字体大小，进而影响内容高度和滚动条
    setTimeout(checkScrollbar, 100);
  }, [checkScrollbar]);

  // 添加滚动事件监听器
  useEffect(() => {
    const editorContent = editorContentRef.current;
    if (!editorContent || !enableAutoScroll) return;

    // 添加滚动事件监听器
    editorContent.addEventListener("scroll", handleUserScroll, {
      passive: true,
    });

    return () => {
      editorContent.removeEventListener("scroll", handleUserScroll);
    };
  }, [handleUserScroll, enableAutoScroll]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      if (userScrollTimeoutRef.current) {
        clearTimeout(userScrollTimeoutRef.current);
      }
    };
  }, []);

  // 处理点击事件
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      if (onClick) {
        onClick(e);
      }
    },
    [onClick]
  );

  // 处理鼠标按下事件
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (onMouseDown) {
        onMouseDown(e);
      }
    },
    [onMouseDown]
  );

  // 如果编辑器还没有初始化完成，显示加载状态
  if (!editor) {
    return (
      <div
        className={`basic-editor-loading ${className}`}
        style={style}
        title={title}
      >
        {placeholder}
      </div>
    );
  }

  return (
    <div
      className={`basic-editor-container ${className}`}
      onClick={handleClick}
      onMouseDown={handleMouseDown}
      style={style}
      title={title}
    >
      {/* 使用官方的 EditorContent 组件渲染编辑器 */}
      <EditorContent
        editor={editor}
        className={`basic-editor-content ${
          enableAutoScroll && !editable ? "streaming" : ""
        }`}
        ref={editorContentRef}
      />

      {/* 智能滚动指示器 - 在AI生成内容时显示 */}
      {isAutoScrolling && enableAutoScroll && (
        <div className="auto-scroll-indicator">
          <div className="scroll-icon">↓</div>
          <span>跟随最新内容</span>
        </div>
      )}

      {/* 工具栏 - 只在编辑状态且启用时显示，放在编辑器内容下方 */}
      {showToolbar && editable && (
        <BasicToolbar editor={editor} className="basic-editor-toolbar" />
      )}
    </div>
  );
};

export default BasicEditor;
